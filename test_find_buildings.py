#!/usr/bin/env python3
"""
Test script for the Find Buildings endpoint with coordinate mesh generation.
"""

import asyncio
import httpx
import json
import sys
import os

# Add backend directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_find_buildings_endpoint():
    """Test the find buildings endpoint directly."""
    print("🧪 Testing Find Buildings Endpoint with Coordinate Mesh")
    print("=" * 60)
    
    # Import the endpoint function and models
    from reality_app import find_buildings, FindBuildingsRequest, MapyCzSuggestion
    
    # Create test MapyCzSuggestion objects
    test_cities = [
        {
            "name": "Praha",
            "type": "regional.municipality",
            "lat": 50.08355,
            "lon": 14.43414,
            "bbox": [14.224, 49.942, 14.707, 50.177]  # Real Prague bbox
        },
        {
            "name": "Brno",
            "type": "regional.municipality", 
            "lat": 49.19522,
            "lon": 16.60796,
            "bbox": [16.428, 49.110, 16.728, 49.294]  # Real Brno bbox
        },
        {
            "name": "Ostrava",
            "type": "regional.municipality",
            "lat": 49.83465,
            "lon": 18.28204,
            "bbox": [18.040, 49.750, 18.520, 49.920]  # Approximate Ostrava bbox
        }
    ]
    
    for city_data in test_cities:
        print(f"🏙️  Testing city: {city_data['name']}")
        
        try:
            # Create MapyCzSuggestion object
            city_suggestion = MapyCzSuggestion(**city_data)
            
            # Create request with different grid densities
            for grid_density in [10, 20]:
                print(f"   📊 Grid density: {grid_density}x{grid_density}")
                
                request = FindBuildingsRequest(
                    selected_city=city_suggestion,
                    grid_density=grid_density,
                    max_points=500
                )
                
                # Call the endpoint function directly
                response = await find_buildings(request)
                
                print(f"   ✅ Success!")
                print(f"      🏢 Buildings found: {response.buildings_found}")
                print(f"      📍 Coordinate points: {response.coordinate_mesh.total_points}")
                print(f"      📐 Area: {response.coordinate_mesh.mesh_area_km2:.2f} km²")
                print(f"      ⏱️  Duration: {response.search_duration_ms:.1f}ms")
                print(f"      📝 Notes: {len(response.notes)} notes")
                
                # Show first few coordinate points
                if response.coordinate_mesh.coordinate_points:
                    print(f"      📌 First 3 coordinate points:")
                    for i, point in enumerate(response.coordinate_mesh.coordinate_points[:3]):
                        print(f"         {i+1}. {point.lat:.6f}, {point.lon:.6f}")
                
                # Show sample buildings
                if response.buildings:
                    print(f"      🏠 Sample buildings:")
                    for i, building in enumerate(response.buildings[:2]):
                        print(f"         {i+1}. {building['address']}")
                        print(f"            Type: {building['property_type']}")
                        print(f"            Coords: {building['coordinates']['lat']:.6f}, {building['coordinates']['lng']:.6f}")
                
                print()
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            import traceback
            traceback.print_exc()
            print()

async def test_with_http_client():
    """Test the endpoint via HTTP (if server is running)."""
    print("🌐 Testing via HTTP Client")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    try:
        async with httpx.AsyncClient() as client:
            # Test health endpoint first
            health_response = await client.get(f"{base_url}/health", timeout=5.0)
            if health_response.status_code == 200:
                print("✅ Server is running")
                
                # Test find-buildings endpoint
                test_request = {
                    "selected_city": {
                        "name": "Praha",
                        "type": "regional.municipality",
                        "lat": 50.08355,
                        "lon": 14.43414,
                        "bbox": [14.224, 49.942, 14.707, 50.177]
                    },
                    "grid_density": 15,
                    "max_points": 300
                }
                
                print(f"🔍 Testing HTTP POST to /api/find-buildings")
                print(f"   Request: {json.dumps(test_request, indent=2)}")
                
                response = await client.post(
                    f"{base_url}/api/find-buildings",
                    json=test_request,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ HTTP Success!")
                    print(f"      🏢 Buildings found: {data['buildings_found']}")
                    print(f"      📍 Coordinate points: {data['coordinate_mesh']['total_points']}")
                    print(f"      📐 Area: {data['coordinate_mesh']['mesh_area_km2']:.2f} km²")
                    print(f"      ⏱️  Duration: {data['search_duration_ms']:.1f}ms")
                    print(f"      📝 Notes: {len(data['notes'])} notes")
                    
                    # Show coordinate mesh details
                    mesh = data['coordinate_mesh']
                    print(f"      📊 Grid: {mesh['grid_density']}x{mesh['grid_density']}")
                    print(f"      📏 Steps: lon={mesh['lon_step']:.6f}, lat={mesh['lat_step']:.6f}")
                    
                else:
                    print(f"   ❌ HTTP Error: {response.status_code}")
                    print(f"      Response: {response.text}")
                    
            else:
                print("❌ Server is not running or not responding")
                
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")

async def main():
    """Main test function."""
    await test_find_buildings_endpoint()
    print("\n" + "=" * 60 + "\n")
    await test_with_http_client()

if __name__ == "__main__":
    asyncio.run(main())
